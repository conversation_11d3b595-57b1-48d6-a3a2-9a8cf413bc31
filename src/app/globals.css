@import 'tailwindcss';
@import 'tw-animate-css';
@import '../styles/theme-colors.css';
@import '../styles/colors.css';
@import '../styles/typography.css';
@import '../styles/layout.css';
@import '../styles/card-styles.css';
@import '../styles/timeline-chart.css';
@import '../styles/button-input-styles.css';
@import '../styles/micro-interactions.css';
@import '../styles/new-charts.css';
@import '../styles/chart-styles.css';
@import '../styles/animations.css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Modern subtle border radius */
  --radius: 0.5rem;

  /* Light mode - Calm and readable color palette */
  /* Background and text colors */
  --background: 216 100% 97%; /* #F0F5FF - Soft Blue-tinted White */
  --foreground: 217 33% 18%; /* #1E293B - Deep Blue-Gray */

  /* Card colors */
  --card: 0 0% 100%; /* #FFFFFF - Pure White */
  --card-foreground: 217 33% 18%; /* #1E293B - Deep Blue-Gray */

  /* Popover colors */
  --popover: 0 0% 100%; /* #FFFFFF - Pure White */
  --popover-foreground: 217 33% 18%; /* #1E293B - Deep Blue-Gray */

  /* Primary colors - Calm Blue */
  --primary: 210 90% 54%; /* #3B82F6 - Calm Blue */
  --primary-foreground: 0 0% 100%; /* White text on blue */
  --primary-light: 210 90% 65%; /* #60A5FA - Lighter blue for hover states */
  --primary-dark: 210 90% 45%; /* #2563EB - Darker blue for active states */

  /* Secondary colors - Soft Slate */
  --secondary: 215 16% 47%; /* #64748B - Soft Slate */
  --secondary-foreground: 0 0% 100%; /* White text on slate */
  --secondary-light: 215 16% 57%; /* #7E8CA2 - Lighter slate for hover states */
  --secondary-dark: 215 16% 37%; /* #4B5563 - Darker slate for active states */

  /* Muted colors for subtle UI elements */
  --muted: 213 60% 97%; /* #EDF2FF - light blue-gray */
  --muted-foreground: 215 25% 47%; /* #64748B - medium navy for subtle text */

  /* Accent colors - using the same teal as primary for consistency */
  --accent: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
  --accent-foreground: 0 0% 100%; /* White text on accent */
  --accent-light: 170 100% 50%; /* #00F5D4 - Lighter teal for hover states */
  --accent-dark: 170 100% 30%; /* #009A85 - Darker teal for active states */

  /* Destructive colors - red */
  --destructive: 0 84% 60%; /* #EF4444 - red for destructive actions */
  --destructive-foreground: 0 0% 100%; /* White text on destructive */
  --destructive-light: 0 84% 80%; /* Lighter red for hover states */
  --destructive-dark: 0 84% 45%; /* Darker red for active states */

  /* Success colors - green */
  --success: 142 76% 36%; /* #22C55E - green for success states */
  --success-foreground: 0 0% 100%; /* White text on success */
  --success-light: 142 76% 75%; /* Lighter green for hover states */
  --success-dark: 142 76% 25%; /* Darker green for active states */

  /* Warning colors - amber */
  --warning: 45 93% 47%; /* #F59E0B - amber for warning states */
  --warning-foreground: 0 0% 100%; /* White text on warning */
  --warning-light: 45 93% 75%; /* Lighter amber for hover states */
  --warning-dark: 45 93% 35%; /* Darker amber for active states */

  /* Info colors - sky blue */
  --info: 199 89% 48%; /* #0EA5E9 - sky blue for info states */
  --info-foreground: 0 0% 100%; /* White text on info */
  --info-light: 199 89% 75%; /* Lighter sky blue for hover states */
  --info-dark: 199 89% 35%; /* Darker sky blue for active states */

  /* Border, input, and ring colors */
  --border: 214 100% 91%; /* #D1E0FF - soft blue for borders/dividers */
  --input: 214 100% 91%; /* #D1E0FF - soft blue for inputs */
  --ring: 217 91% 60%; /* #3B82F6 - vibrant blue for focus rings */

  /* Chart colors - expanded palette */
  --chart-1: 217 91% 60%; /* #3B82F6 - vibrant blue */
  --chart-2: 152 60% 36%; /* #10B981 - emerald green */
  --chart-3: 199 89% 48%; /* #0EA5E9 - sky blue */
  --chart-4: 142 76% 36%; /* #22C55E - green */
  --chart-5: 245 58% 51%; /* #6366F1 - indigo */
  --chart-6: 326 73% 62%; /* #EC4899 - pink */
  --chart-7: 45 93% 47%; /* #F59E0B - amber */
  --chart-8: 0 84% 60%; /* #EF4444 - red */

  /* Sidebar colors */
  --sidebar: 0 0% 100%; /* #FFFFFF - Pure White for sidebar */
  --sidebar-foreground: 215 30% 17%; /* #1E2A38 - Deep Navy Blue */
  --sidebar-primary: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
  --sidebar-primary-foreground: 0 0% 100%; /* White text */
  --sidebar-accent: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
  --sidebar-accent-foreground: 0 0% 100%; /* White text */
  --sidebar-border: 220 13% 91%; /* #E5E7EB - soft gray */
  --sidebar-ring: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
}

.dark {
  /* Dark mode - Calm and readable color palette */
  /* Background and text colors */
  --background: 215 28% 12%; /* #161C26 - Deep Blue-Gray */
  --foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */

  /* Card colors */
  --card: 215 25% 18%; /* #232A36 - Dark Blue-Gray */
  --card-foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */

  /* Popover colors */
  --popover: 215 25% 18%; /* #232A36 - Dark Blue-Gray */
  --popover-foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */

  /* Primary colors - Calm Blue adjusted for dark mode */
  --primary: 210 90% 54%; /* #3B82F6 - Calm Blue */
  --primary-foreground: 0 0% 100%; /* White text */
  --primary-light: 210 90% 65%; /* #60A5FA - Lighter blue for hover states */
  --primary-dark: 210 90% 45%; /* #2563EB - Darker blue for active states */

  /* Secondary colors - Soft Slate adjusted for dark mode */
  --secondary: 215 16% 57%; /* #7E8CA2 - Soft Slate */
  --secondary-foreground: 0 0% 100%; /* White text */
  --secondary-light: 215 16% 67%; /* #99A6BA - Lighter slate for hover states */
  --secondary-dark: 215 16% 47%; /* #64748B - Darker slate for active states */

  /* Muted colors for subtle UI elements */
  --muted: 220 15% 16%; /* #232A3B - dark muted background */
  --muted-foreground: 210 15% 70%; /* #A1A9B8 - light gray for muted text */

  /* Accent colors - using the same teal as primary for consistency */
  --accent: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
  --accent-foreground: 0 0% 100%; /* White text */
  --accent-light: 170 100% 45%; /* #00DBC0 - Lighter teal for hover states */
  --accent-dark: 170 100% 32%; /* #00A38C - Darker teal for active states */

  /* Destructive colors - red */
  --destructive: 0 84% 60%; /* #EF4444 - red */
  --destructive-foreground: 0 0% 100%; /* White text */
  --destructive-light: 0 84% 70%; /* Lighter red for hover states */
  --destructive-dark: 0 84% 50%; /* Darker red for active states */

  /* Success colors - green */
  --success: 142 76% 36%; /* #22C55E - green */
  --success-foreground: 0 0% 100%; /* White text */
  --success-light: 142 76% 45%; /* Lighter green for hover states */
  --success-dark: 142 76% 30%; /* Darker green for active states */

  /* Warning colors - amber */
  --warning: 45 93% 47%; /* #F59E0B - amber */
  --warning-foreground: 0 0% 100%; /* White text */
  --warning-light: 45 93% 55%; /* Lighter amber for hover states */
  --warning-dark: 45 93% 40%; /* Darker amber for active states */

  /* Info colors - sky blue */
  --info: 199 89% 48%; /* #0EA5E9 - sky blue */
  --info-foreground: 0 0% 100%; /* White text */
  --info-light: 199 89% 55%; /* Lighter sky blue for hover states */
  --info-dark: 199 89% 40%; /* Darker sky blue for active states */

  /* Border, input, and ring colors */
  --border: 217 19% 27%; /* #2E374A - darker border color */
  --input: 217 19% 27%; /* #2E374A - darker input background */
  --ring: 217 91% 60%; /* #3B82F6 - vibrant blue for focus rings */

  /* Chart colors - expanded palette */
  --chart-1: 217 91% 60%; /* #3B82F6 - vibrant blue */
  --chart-2: 152 60% 36%; /* #10B981 - emerald green */
  --chart-3: 199 89% 48%; /* #0EA5E9 - sky blue */
  --chart-4: 142 76% 36%; /* #22C55E - green */
  --chart-5: 245 58% 51%; /* #6366F1 - indigo */
  --chart-6: 326 73% 62%; /* #EC4899 - pink */
  --chart-7: 45 93% 47%; /* #F59E0B - amber */
  --chart-8: 0 84% 60%; /* #EF4444 - red */

  /* Sidebar colors */
  --sidebar: 220 15% 7%; /* #0E1116 - Dark Charcoal */
  --sidebar-foreground: 210 20% 98%; /* #F5F7FA - Soft White */
  --sidebar-primary: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
  --sidebar-primary-foreground: 0 0% 100%; /* White text */
  --sidebar-accent: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
  --sidebar-accent-foreground: 0 0% 100%; /* White text */
  --sidebar-border: 217 19% 27%; /* #2E374A - darker border color */
  --sidebar-ring: 170 100% 38%; /* #00C2A8 - Vibrant Teal */
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }

  /* Modern scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--background));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Datetime input styles for dark theme */
  input[type='datetime-local'] {
    color-scheme: dark;
  }
}

/* Modern design utilities */
@layer utilities {
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-accent/40 focus:ring-offset-2 focus:ring-offset-background transition-colors;
  }

  .card-hover {
    @apply hover:shadow-md transition-all duration-200;
  }

  .interactive-element {
    @apply transition-all duration-200 hover:scale-[1.02] active:scale-[0.98];
  }

  /* Custom classes for chart bars */
  .chart-bar-h-0 {
    height: 0%;
  }
  .chart-bar-h-10 {
    height: 10%;
  }
  .chart-bar-h-20 {
    height: 20%;
  }
  .chart-bar-h-30 {
    height: 30%;
  }
  .chart-bar-h-40 {
    height: 40%;
  }
  .chart-bar-h-50 {
    height: 50%;
  }
  .chart-bar-h-60 {
    height: 60%;
  }
  .chart-bar-h-70 {
    height: 70%;
  }
  .chart-bar-h-80 {
    height: 80%;
  }
  .chart-bar-h-90 {
    height: 90%;
  }
  .chart-bar-h-100 {
    height: 100%;
  }

  /* Custom classes for positioning */
  .left-0 {
    left: 0%;
  }
  .left-10 {
    left: 10%;
  }
  .left-20 {
    left: 20%;
  }
  .left-30 {
    left: 30%;
  }
  .left-40 {
    left: 40%;
  }
  .left-50 {
    left: 50%;
  }
  .left-60 {
    left: 60%;
  }
  .left-70 {
    left: 70%;
  }
  .left-80 {
    left: 80%;
  }
  .left-90 {
    left: 90%;
  }
  .left-100 {
    left: 100%;
  }

  /* Timeline item width */
  .min-w-timeline-item {
    min-width: 80px;
  }

  /* Progress bar widths */
  .w-progress-0 {
    width: 0%;
  }
  .w-progress-10 {
    width: 10%;
  }
  .w-progress-20 {
    width: 20%;
  }
  .w-progress-30 {
    width: 30%;
  }
  .w-progress-40 {
    width: 40%;
  }
  .w-progress-50 {
    width: 50%;
  }
  .w-progress-60 {
    width: 60%;
  }
  .w-progress-70 {
    width: 70%;
  }
  .w-progress-80 {
    width: 80%;
  }
  .w-progress-90 {
    width: 90%;
  }
  .w-progress-100 {
    width: 100%;
  }

  /* Scrollbar styling for timeline */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* Hide scrollbar but keep functionality */
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* Map container sizing */
  .h-full {
    height: 100%;
  }

  .w-full {
    width: 100%;
  }

  /* Map marker styles - modern */
  .map-marker {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .map-marker-normal {
    width: 24px;
    height: 24px;
    border: 1px solid hsl(var(--border));
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-size: 12px;
    font-weight: medium;
  }

  .map-marker-selected {
    width: 32px;
    height: 32px;
    border: 2px solid hsl(var(--primary));
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: scale(1.1);
  }

  /* Map popup styles */
  .map-popup {
    min-width: 200px;
  }

  .map-popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .map-popup-icon {
    width: 50px;
    height: 50px;
  }

  .map-popup-temp {
    font-weight: bold;
    font-size: 16px;
  }

  .map-popup-feels {
    font-size: 12px;
  }

  .map-popup-detail {
    font-size: 14px;
    margin-bottom: 5px;
  }
}
