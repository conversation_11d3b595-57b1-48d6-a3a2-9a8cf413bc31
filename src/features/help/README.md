# Help Feature

This folder contains components for providing help and documentation to users.

## Components

- `UserGuide`: Component for displaying user guide information.

## Usage

```tsx
import { UserGuide } from '@/features/help/components';

const MyComponent = () => {
  return <UserGuide />;
};
```

## Features

- Comprehensive user guide with step-by-step instructions
- Interactive examples
- Searchable content
- Collapsible sections for better organization
- Responsive design for different screen sizes
