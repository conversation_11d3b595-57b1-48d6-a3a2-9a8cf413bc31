/**
 * Sunride Theme Color System - Modern Design
 * Calm and readable color palette with soft blues and neutrals
 * These variables can be used throughout the application
 */

:root {
  /* Base theme colors - Light Mode */
  --color-bg: #F5F8FC;        /* Soft Blue-tinted White */
  --color-text: #364356;      /* Deep Blue-Gray */
  --color-accent: #3B82F6;    /* Calm Blue */
  --color-card: #FFFFFF;      /* Pure White */
  --color-shadow: rgba(0, 0, 0, 0.05); /* Subtle shadow */

  /* Derived colors for UI elements */
  --color-accent-light: #60A5FA; /* Lighter blue */
  --color-accent-dark: #2563EB;  /* Darker blue */
  --color-border: #E2E8F4;       /* Soft Blue-Gray for borders */
  --color-muted: #F1F5FB;        /* Soft Blue-Gray */
  --color-muted-text: #526580;   /* Medium Blue-Gray */

  /* Map theme colors to existing system for compatibility */
  --theme-bg: var(--color-bg);
  --theme-text: var(--color-text);
  --theme-accent: var(--color-accent);
  --theme-card: var(--color-card);
  --theme-shadow: var(--color-shadow);

  /* Map to HSL variables for Tailwind */
  --background: 210 30% 98%;    /* #F5F8FC - Soft Blue-tinted White */
  --foreground: 215 25% 27%;    /* #364356 - Deep Blue-Gray */
  --primary: 210 90% 54%;       /* #3B82F6 - Calm Blue */
  --primary-foreground: 0 0% 100%;
  --card: 0 0% 100%;            /* #FFFFFF - Pure White */
  --card-foreground: 215 25% 27%; /* #364356 - Deep Blue-Gray */
  --border: 210 30% 92%;        /* #E2E8F4 - Soft Blue-Gray */
  --input: 210 30% 92%;         /* #E2E8F4 - Soft Blue-Gray */
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Base theme colors - Dark Mode */
    --color-bg: #161C26;        /* Deep Blue-Gray */
    --color-text: #F5F9FF;      /* Soft Blue-White */
    --color-accent: #3B82F6;    /* Calm Blue */
    --color-card: #232A36;      /* Dark Blue-Gray */
    --color-shadow: rgba(0, 0, 0, 0.2); /* Subtle shadow */

    /* Derived colors for UI elements */
    --color-accent-light: #60A5FA; /* Lighter blue */
    --color-accent-dark: #2563EB;  /* Darker blue */
    --color-border: #364356;       /* Medium Blue-Gray for borders */
    --color-muted: #2F3A4A;        /* Dark Blue-Gray */
    --color-muted-text: #B4CCEB;   /* Light Blue-Gray */

    /* Map to HSL variables for Tailwind */
    --background: 215 28% 12%;    /* #161C26 - Deep Blue-Gray */
    --foreground: 210 40% 98%;    /* #F5F9FF - Soft Blue-White */
    --primary: 210 90% 54%;       /* #3B82F6 - Calm Blue */
    --primary-foreground: 0 0% 100%;
    --card: 215 25% 18%;          /* #232A36 - Dark Blue-Gray */
    --card-foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */
    --border: 215 25% 27%;        /* #364356 - Medium Blue-Gray */
    --input: 215 25% 27%;         /* #364356 - Medium Blue-Gray */
  }
}

/* For next-themes or manual class-based dark mode */
.dark {
  /* Base theme colors - Dark Mode */
  --color-bg: #161C26;        /* Deep Blue-Gray */
  --color-text: #F5F9FF;      /* Soft Blue-White */
  --color-accent: #3B82F6;    /* Calm Blue */
  --color-card: #232A36;      /* Dark Blue-Gray */
  --color-shadow: rgba(0, 0, 0, 0.2); /* Subtle shadow */

  /* Derived colors for UI elements */
  --color-accent-light: #60A5FA; /* Lighter blue */
  --color-accent-dark: #2563EB;  /* Darker blue */
  --color-border: #364356;       /* Medium Blue-Gray for borders */
  --color-muted: #2F3A4A;        /* Dark Blue-Gray */
  --color-muted-text: #B4CCEB;   /* Light Blue-Gray */

  /* Map to HSL variables for Tailwind */
  --background: 215 28% 12%;    /* #161C26 - Deep Blue-Gray */
  --foreground: 210 40% 98%;    /* #F5F9FF - Soft Blue-White */
  --primary: 210 90% 54%;       /* #3B82F6 - Calm Blue */
  --primary-foreground: 0 0% 100%;
  --card: 215 25% 18%;          /* #232A36 - Dark Blue-Gray */
  --card-foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */
  --border: 215 25% 27%;        /* #364356 - Medium Blue-Gray */
  --input: 215 25% 27%;         /* #364356 - Medium Blue-Gray */
}
