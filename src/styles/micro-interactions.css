/* Flat Design - No Micro Interactions */

/* Base containers - no transitions */
.container-transition {
  /* No transitions in flat design */
}

/* Card states - no interactions */
.card-interactive {
  /* No interactions in flat design */
}

/* Button states - no interactions */
.button-interactive {
  /* No interactions in flat design */
}

/* Link states - no interactions */
.link-interactive {
  /* No interactions in flat design */
}

/* Input states - no interactions */
.input-interactive {
  /* No interactions in flat design */
}

/* No hover scale effect */
.hover-scale {
  /* No scale effect in flat design */
}

/* No hover lift effect */
.hover-lift {
  /* No lift effect in flat design */
}

/* No animations in flat design */
.fade-in {
  /* No fade-in animation in flat design */
}

.pulse-animation {
  /* No pulse animation in flat design */
}

.bounce-animation {
  /* No bounce animation in flat design */
}

/* Keep spin animation only for loading indicators */
.spin-animation {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
