/* Direct styles for chart canvas elements - Flat Design */
canvas.humidity-chart,
canvas.pressure-chart,
canvas.uv-chart {
  border: 1px solid var(--color-border);
  background-color: transparent;
}

/* Force text color for chart elements */
.chart-js-text {
  fill: var(--color-text) !important;
  color: var(--color-text) !important;
  stroke: none !important;
}

/* Force line colors */
.chart-js-line {
  stroke-width: 1px !important;
}

/* Force point colors */
.chart-js-point {
  r: 4 !important; /* Point radius */
  stroke-width: 1px !important;
  stroke: var(--color-text) !important;
}

/* UV Index legend colors - using risk level colors */
.uv-low {
  background-color: var(--chart-uv-low);
}

.uv-moderate {
  background-color: var(--chart-uv-moderate);
}

.uv-high {
  background-color: var(--chart-uv-high);
}

.uv-very-high {
  background-color: var(--chart-uv-very-high);
}

.uv-extreme {
  background-color: var(--chart-uv-extreme);
}
