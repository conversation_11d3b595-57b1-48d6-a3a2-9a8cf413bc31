/* Button & Input Styles - Flat Design */

/* Base button styles */
.btn {
  @apply inline-flex items-center justify-center text-sm font-medium;
  @apply bg-[var(--color-accent)] text-white px-4 py-2;
  @apply focus:outline-none focus-visible:ring-1 focus-visible:ring-accent;
}

/* Button variants */
.btn-primary {
  @apply bg-[var(--color-accent)] text-white;
}

.btn-secondary {
  @apply bg-[var(--color-secondary)] text-white;
}

.btn-outline {
  @apply bg-transparent border border-[var(--color-accent)] text-[var(--color-accent)];
}

.btn-ghost {
  @apply bg-transparent text-[var(--color-accent)];
}

.btn-destructive {
  @apply bg-red-500 text-white;
}

/* Button sizes */
.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-icon {
  @apply p-2 aspect-square;
}

/* Button states */
.btn-disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

.btn-loading {
  @apply relative text-transparent transition-none hover:text-transparent;
}

.btn-loading::after {
  @apply absolute h-5 w-5 animate-spin border-2 border-current border-t-transparent;
  content: "";
}

/* Input styles */
.input {
  @apply w-full border border-border bg-transparent px-4 py-2 text-sm;
  @apply focus:outline-none focus-visible:ring-1 focus-visible:ring-accent;
  @apply placeholder:text-muted disabled:opacity-50 disabled:cursor-not-allowed;
}

.input-with-icon-left {
  @apply pl-10;
}

.input-with-icon-right {
  @apply pr-10;
}

.input-error {
  @apply border-red-500 focus-visible:ring-red-500;
}

.input-success {
  @apply border-green-500 focus-visible:ring-green-500;
}

/* Icon positioning */
.input-icon-left {
  @apply absolute left-3 top-1/2 -translate-y-1/2 text-muted pointer-events-none;
}

.input-icon-right {
  @apply absolute right-3 top-1/2 -translate-y-1/2 text-muted;
}

/* Form group */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-foreground;
}

.form-helper {
  @apply text-xs text-muted mt-1;
}

.form-error {
  @apply text-xs text-red-500 mt-1;
}
