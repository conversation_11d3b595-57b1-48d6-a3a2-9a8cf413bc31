/* Card Styles - Flat Design */

.card-base {
  @apply bg-white dark:bg-[var(--color-card)] border border-border p-4 md:p-6 space-y-2;
}

.card-header {
  @apply flex items-center justify-between;
}

.card-title {
  @apply text-lg font-medium text-foreground;
}

.card-content {
  @apply space-y-2;
}

.card-text {
  @apply text-sm text-muted;
}

.card-footer {
  @apply flex items-center justify-between pt-2 mt-2;
}

/* Card variants */
.card-primary {
  @apply border-l-4 border-primary;
}

.card-secondary {
  @apply border-l-4 border-secondary;
}

.card-accent {
  @apply border-l-4 border-accent;
}

.card-info {
  @apply border-l-4 border-blue-500;
}

.card-success {
  @apply border-l-4 border-green-500;
}

.card-warning {
  @apply border-l-4 border-yellow-500;
}

.card-error {
  @apply border-l-4 border-red-500;
}

/* Card with hover effect - removed for flat design */
.card-hoverable {
  @apply border border-border;
}

/* Card with minimal outline */
.card-outline {
  @apply border border-border;
}

/* Card with icon */
.card-icon {
  @apply flex items-center gap-3;
}

/* Responsive card grid */
.card-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
}

/* Card with action buttons */
.card-actions {
  @apply flex items-center justify-end gap-2 mt-4;
}
