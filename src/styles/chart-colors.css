/* Chart-specific colors for both light and dark modes - Flat Design */

/* Chart canvas elements */
canvas {
  /* No filters in flat design */
}

/* Chart container styles */
.chart-container {
  background-color: transparent;
  padding: 8px;
  border: 1px solid var(--color-border);
}

/* Chart legend styles */
.chart-legend,
.chartjs-legend {
  color: var(--color-text) !important;
}

/* Chart tooltip styles */
.chartjs-tooltip,
.chart-tooltip {
  background-color: var(--color-card) !important;
  color: var(--color-text) !important;
  border: 1px solid var(--color-border) !important;
}

/* Chart axis styles */
.chart-axis,
.chartjs-axis-title,
.chartjs-axis-label {
  color: var(--color-text) !important;
}

/* Chart grid styles */
.chart-grid,
.chartjs-grid-line {
  stroke: var(--color-border) !important;
}

/* Chart point styles */
.chart-point,
.chartjs-point {
  stroke: var(--color-text) !important;
  stroke-width: 1px !important;
}

/* Chart line styles */
.chart-line,
.chartjs-line {
  stroke-width: 1px !important;
}

/* Chart area styles */
.chart-area,
.chartjs-area {
  opacity: 0.8 !important;
}

/* Chart bar styles */
.chart-bar,
.chartjs-bar {
  opacity: 1.0 !important;
}
