/* Typography System - Modern and Readable */

html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: hsl(var(--foreground));
  letter-spacing: -0.025em;
}

p, span, label {
  font-weight: 400;
  line-height: 1.6;
  color: hsl(var(--foreground) / 0.9);
}

/* Typography utility classes */
.text-body {
  @apply text-base text-foreground/90 leading-relaxed;
}

.text-body-lg {
  @apply text-lg text-foreground/90 leading-relaxed;
}

.text-body-sm {
  @apply text-sm text-foreground/90 leading-relaxed;
}

.text-label {
  @apply text-sm font-medium text-muted-foreground leading-normal;
}

.text-caption {
  @apply text-xs text-muted-foreground leading-normal;
}

/* Maintain semantic heading levels with refined typography */
h1 {
  @apply text-3xl font-semibold leading-tight tracking-tight;
}

h2 {
  @apply text-2xl font-semibold leading-tight tracking-tight;
}

h3 {
  @apply text-xl font-semibold leading-snug;
}

h4 {
  @apply text-lg font-semibold leading-snug;
}

h5 {
  @apply text-base font-semibold leading-normal;
}

h6 {
  @apply text-sm font-semibold leading-normal;
}

/* Additional typography styles */
.text-display {
  @apply text-4xl font-bold leading-none tracking-tight;
}

.text-display-lg {
  @apply text-5xl font-bold leading-none tracking-tight;
}

.text-link {
  @apply text-primary hover:text-primary/80 hover:underline transition-colors;
}
