/* Map Styles */

.map-container {
  position: relative;
  height: 100%;
  width: 100%;
  background-color: #f1f5f9;
  border-radius: 1rem;
  overflow: hidden;
}

.dark .map-container {
  background-color: #1e293b;
}

.map-route {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
}

.map-marker {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-weight: 500;
  transform: translate(-50%, -50%);
}

.map-marker-normal {
  background-color: #FFFFFF;
  border: 1px solid #DADADA;
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.75rem;
  z-index: 10;
}

.map-marker-selected {
  background-color: #000000;
  color: #FFFFFF;
  width: 2rem;
  height: 2rem;
  font-size: 0.875rem;
  z-index: 20;
}

.map-grid-vertical-1 {
  position: absolute;
  left: 25%;
  height: 100%;
  border-right: 1px solid #cbd5e1;
}

.map-grid-vertical-2 {
  position: absolute;
  left: 50%;
  height: 100%;
  border-right: 1px solid #cbd5e1;
}

.map-grid-vertical-3 {
  position: absolute;
  left: 75%;
  height: 100%;
  border-right: 1px solid #cbd5e1;
}

.map-grid-horizontal-1 {
  position: absolute;
  top: 25%;
  width: 100%;
  border-bottom: 1px solid #cbd5e1;
}

.map-grid-horizontal-2 {
  position: absolute;
  top: 50%;
  width: 100%;
  border-bottom: 1px solid #cbd5e1;
}

.map-grid-horizontal-3 {
  position: absolute;
  top: 75%;
  width: 100%;
  border-bottom: 1px solid #cbd5e1;
}

.dark .map-grid-vertical-1,
.dark .map-grid-vertical-2,
.dark .map-grid-vertical-3,
.dark .map-grid-horizontal-1,
.dark .map-grid-horizontal-2,
.dark .map-grid-horizontal-3 {
  border-color: #334155;
}

.map-attribution {
  position: absolute;
  bottom: 0.25rem;
  right: 0.25rem;
  font-size: 0.75rem;
  color: #64748b;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0 0.25rem;
  border-radius: 0.25rem;
  z-index: 20;
}

.dark .map-attribution {
  background-color: rgba(0, 0, 0, 0.3);
  color: #94a3b8;
}

/* Custom map styles */
.map-popup {
  position: absolute;
  background-color: white;
  padding: 0.5rem;
  font-size: 0.875rem;
  border: 1px solid #DADADA;
  pointer-events: none;
  transform: translateX(-50%);
}

.dark .map-popup {
  background-color: #1f2937;
  border-color: rgba(255, 255, 255, 0.1);
}

.map-info-panel {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  width: 16rem;
  background-color: #FFFFFF;
  padding: 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #DADADA;
}

.dark .map-info-panel {
  background-color: rgba(31, 41, 55, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  .map-info-panel {
    left: 0.5rem;
    right: 0.5rem;
    width: auto;
  }
}

/* Animation delay classes for loading spinner dots */
.animation-delay-0 {
  animation-delay: 0s;
}

.animation-delay-1 {
  animation-delay: 0.15s;
}

.animation-delay-2 {
  animation-delay: 0.3s;
}

/* Dynamic positioning classes for map elements */
.map-marker-position {
  position: absolute;
  transform: translate(-50%, -50%);
}

/* For BasicMap markers that only need horizontal positioning */
.marker-top-offset {
  position: absolute;
  top: -0.75rem; /* equivalent to -top-3 in Tailwind */
  transform: translateX(-50%);
}

/* Position classes for markers (0-100%) */
.marker-pos-0 {
  left: 0%;
}
.marker-pos-5 {
  left: 5%;
}
.marker-pos-10 {
  left: 10%;
}
.marker-pos-15 {
  left: 15%;
}
.marker-pos-20 {
  left: 20%;
}
.marker-pos-25 {
  left: 25%;
}
.marker-pos-30 {
  left: 30%;
}
.marker-pos-35 {
  left: 35%;
}
.marker-pos-40 {
  left: 40%;
}
.marker-pos-45 {
  left: 45%;
}
.marker-pos-50 {
  left: 50%;
}
.marker-pos-55 {
  left: 55%;
}
.marker-pos-60 {
  left: 60%;
}
.marker-pos-65 {
  left: 65%;
}
.marker-pos-70 {
  left: 70%;
}
.marker-pos-75 {
  left: 75%;
}
.marker-pos-80 {
  left: 80%;
}
.marker-pos-85 {
  left: 85%;
}
.marker-pos-90 {
  left: 90%;
}
.marker-pos-95 {
  left: 95%;
}
.marker-pos-100 {
  left: 100%;
}

/* Position classes for 2D markers */
.marker-x-0 {
  left: 0%;
}
.marker-x-10 {
  left: 10%;
}
.marker-x-20 {
  left: 20%;
}
.marker-x-30 {
  left: 30%;
}
.marker-x-40 {
  left: 40%;
}
.marker-x-50 {
  left: 50%;
}
.marker-x-60 {
  left: 60%;
}
.marker-x-70 {
  left: 70%;
}
.marker-x-80 {
  left: 80%;
}
.marker-x-90 {
  left: 90%;
}
.marker-x-100 {
  left: 100%;
}

.marker-y-0 {
  top: 0%;
}
.marker-y-10 {
  top: 10%;
}
.marker-y-20 {
  top: 20%;
}
.marker-y-30 {
  top: 30%;
}
.marker-y-40 {
  top: 40%;
}
.marker-y-50 {
  top: 50%;
}
.marker-y-60 {
  top: 60%;
}
.marker-y-70 {
  top: 70%;
}
.marker-y-80 {
  top: 80%;
}
.marker-y-90 {
  top: 90%;
}
.marker-y-100 {
  top: 100%;
}

/* Grid line positioning classes */
.grid-line-vertical-25 {
  position: absolute;
  left: 25%;
  height: 100%;
}

.grid-line-vertical-50 {
  position: absolute;
  left: 50%;
  height: 100%;
}

.grid-line-vertical-75 {
  position: absolute;
  left: 75%;
  height: 100%;
}

.grid-line-horizontal-25 {
  position: absolute;
  top: 25%;
  width: 100%;
}

.grid-line-horizontal-50 {
  position: absolute;
  top: 50%;
  width: 100%;
}

.grid-line-horizontal-75 {
  position: absolute;
  top: 75%;
  width: 100%;
}
