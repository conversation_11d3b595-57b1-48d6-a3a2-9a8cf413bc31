.progress-bar {
  height: 100%;
  background-color: hsl(var(--primary));
  transition: all 0.3s;
}

/* Progress bar width classes */
.progress-width-0 {
  width: 0%;
}
.progress-width-10 {
  width: 10%;
}
.progress-width-20 {
  width: 20%;
}
.progress-width-25 {
  width: 25%;
}
.progress-width-30 {
  width: 30%;
}
.progress-width-33 {
  width: 33.33%;
}
.progress-width-40 {
  width: 40%;
}
.progress-width-50 {
  width: 50%;
}
.progress-width-60 {
  width: 60%;
}
.progress-width-66 {
  width: 66.66%;
}
.progress-width-70 {
  width: 70%;
}
.progress-width-75 {
  width: 75%;
}
.progress-width-80 {
  width: 80%;
}
.progress-width-90 {
  width: 90%;
}
.progress-width-100 {
  width: 100%;
}
