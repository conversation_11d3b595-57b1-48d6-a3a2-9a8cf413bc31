# Required API Keys and Credentials
# --------------------------------------
# OpenWeather API Key - Get one at https://openweathermap.org/api
OPENWEATHER_API_KEY=your_openweather_api_key_here

# MongoDB URI - Format: mongodb+srv://<username>:<password>@<cluster>.<id>.mongodb.net/?retryWrites=true&w=majority
MONGODB_URI=your_mongodb_connection_string_here

# Application Settings
# --------------------------------------
# Port for Next.js to run on (default is 3000)
PORT=3000

# Rate Limiting
# --------------------------------------
# Maximum number of requests per minute per IP (default: 60)
RATE_LIMIT_MAX=60
# Time window in milliseconds (default: 60000 - 1 minute)
RATE_LIMIT_WINDOW=60000

# Caching
# --------------------------------------
# Cache duration in milliseconds (default: 3600000 - 1 hour)
CACHE_DURATION=3600000

# Debug and Development
# --------------------------------------
# Set to 'true' to enable detailed logging
DEBUG=false
# Set to 'development' or 'production'
NODE_ENV=development 