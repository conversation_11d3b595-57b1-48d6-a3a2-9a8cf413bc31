{"name": "sunride", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:file": "node scripts/run-test.js", "check:types": "tsc --noEmit", "check:ts": "node scripts/check-typescript.js", "check:any": "node scripts/check-any-types.js", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "verify:deployment": "node scripts/verify-deployment.js", "deploy": "npm run verify:deployment && git push origin main", "vercel:verify": "node scripts/vercel-build-verify.js", "compare:builds": "node scripts/compare-builds.js"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "autoprefixer": "^10.4.21", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "leaflet-gpx": "^2.2.0", "leaflet.heat": "^0.2.0", "limiter": "^2.1.0", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "next": "^15.3.3", "next-themes": "^0.4.6", "ol": "^10.5.0", "pdf-lib": "^1.17.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-leaflet": "^5.0.0", "recharts": "^2.15.3", "server-only": "^0.0.1", "shadcn-ui": "^0.9.5", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.2", "xml2js": "^0.6.2", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/leaflet": "^1.9.18", "@types/node": "^22.15.27", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/xml2js": "^0.4.14", "eslint": "^9.27.0", "eslint-config-next": "^15.3.3", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "typescript": "^5.8.3"}}